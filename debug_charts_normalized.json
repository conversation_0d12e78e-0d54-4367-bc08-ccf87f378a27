[{"id": "10004", "accno": "1000", "description": "<PERSON><PERSON>", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "13289", "accno": "10001", "description": "Kasse - Energie", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "22139", "accno": "10002", "description": "Kasse_TEST", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "11113", "accno": "1010", "description": "Post CHF CH88 0900 0000 1506 3577 9 (1010)", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12644", "accno": "1011", "description": "POST CHF CH780900000085508080F (1011)", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10005", "accno": "1020", "description": "Aareal Bank AG EUR CH2689144245632531516 (1020)", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "14007", "accno": "1021", "description": "ZKB CAD CH63 0070 0114 5000 5131 6 (1022)", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12370", "accno": "1022", "description": "UBS CHF CH63 0070 0114 5000 5131 6 (1020)", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12465", "accno": "1023", "description": "UBS CH63 0070 0114 7510 5445 1", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "13552", "accno": "1024", "description": "UBS CH64 0070 0114 7510 121211", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "12252", "accno": "1026", "description": "USD-Bankkonto", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12226", "accno": "1027", "description": "ZKB CH63 0070 0114 7510 68s6s7dg6", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12718", "accno": "1028", "description": "UBS EUR CH12 3456 7890 1234 5678 9 (1028)", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10704", "accno": "1030", "description": "<PERSON><PERSON>", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12374", "accno": "1060", "description": "Wertschriften mit Börsenkurs", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10008", "accno": "1090", "description": "Transferkonto", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "13002", "accno": "1098", "description": "<PERSON><PERSON><PERSON> Beträ<PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10009", "accno": "1099", "description": "Unklare Beträge", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10011", "accno": "1100", "description": "Forderungen Schweiz", "link": "AR", "allow_gl": "false", "is_active": "true"}, {"id": "22138", "accno": "11001", "description": "Forderungen Schweiz_TEST", "link": "AR", "allow_gl": "false", "is_active": "true"}, {"id": "10012", "accno": "1101", "description": "Forderungen Ausland", "link": "AR", "allow_gl": "false", "is_active": "true"}, {"id": "10013", "accno": "1102", "description": "Forderungen Ausland Fremdwährungen", "link": "AR", "allow_gl": "false", "is_active": "true"}, {"id": "10014", "accno": "1103", "description": "Forderungen alt", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "13843", "accno": "1104", "description": "Forderungen - Test FW-Bewertung MERC", "link": "AR", "allow_gl": "false", "is_active": "true"}, {"id": "14000", "accno": "1107", "description": "Durchlaufkonto SumUp", "link": "AR", "allow_gl": "false", "is_active": "true"}, {"id": "14005", "accno": "1107.1", "description": "Durchlaufkonto Sumup", "link": "AR_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10713", "accno": "11080", "description": "Durchlaufkonto Mobiliar", "link": "AR_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10015", "accno": "1109", "description": "Wertberichtigungen Forderungen aus Lieferungen und Leistungen gegenüber Dritten", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10817", "accno": "11090", "description": "Durchlaufkonto Steffi", "link": "AR_amount", "allow_gl": "true", "is_active": "true"}, {"id": "12366", "accno": "11091", "description": "Durchlaufkonto Hallo", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "12466", "accno": "11092", "description": "Durchlaufkonto Hallo", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "13553", "accno": "11094", "description": "Durchlaufkonto Hallo Test", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "12215", "accno": "11095", "description": "Durchlaufkonto TEST", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "13373", "accno": "11096", "description": "Durchlaufkonto Stripe", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "10017", "accno": "1110", "description": "Forderungen aus Lieferungen und Leistungen gegenüber Beteiligungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "13001", "accno": "1111", "description": "test pascal", "link": "AR_amount:IC_income:IC_sale", "allow_gl": "false", "is_active": "true"}, {"id": "10252", "accno": "1120", "description": "Forderungen aus Lieferungen und Leistungen gegenüber Beteiligten", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10278", "accno": "1170", "description": "Vorsteuer 8.0% auf Mat. + DL", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10788", "accno": "11700", "description": "Vorsteuer 7.7% auf Mat. + DL", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21369", "accno": "11701", "description": "Vorsteuer 8.1% auf Mat. + DL", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10879", "accno": "11710", "description": "Zoll auf Mat. + DL", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10880", "accno": "11711", "description": "Zoll auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10279", "accno": "1172", "description": "Vorsteuer 2.5% auf Mat. + DL", "link": "AP_tax:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "21370", "accno": "11721", "description": "Vorsteuer 2.6% auf Mat. + DL", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10280", "accno": "1173", "description": "Vorsteuer 7.7% auf Inv. + übr. BA", "link": "AP_tax:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10789", "accno": "11730", "description": "Vorsteuer 7.7% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21371", "accno": "11731", "description": "Vorsteuer 8.1% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10281", "accno": "1174", "description": "Vorsteuer 3.8% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10790", "accno": "11740", "description": "Vorsteuer 3.7% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21372", "accno": "11741", "description": "Vorsteuer 3.8% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10282", "accno": "1175", "description": "Vorsteuer 2.5% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "12371", "accno": "11750", "description": "Vorsteuer 1.1% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21373", "accno": "11751", "description": "Vorsteuer 2.6% auf Inv. + übr. BA", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10283", "accno": "1176", "description": "Vorsteuer 8,0% Bezugsteuer", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10791", "accno": "11760", "description": "Vorsteuer 7,7% Bezugssteuer", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21374", "accno": "11761", "description": "Vorsteuer 8.1% Bezugsteuer", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10021", "accno": "1177", "description": "Forderungen gegenüber Oberzolldirektion", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "22189", "accno": "11779", "description": "Leistungen im Ausland und Leistungen nach Art.90", "link": "AR_tax:AP_tax:IC_taxpart", "allow_gl": "true", "is_active": "true"}, {"id": "10641", "accno": "1178", "description": "Vorsteuer Test 5.0%", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10022", "accno": "1179", "description": "Verrechnungssteuer", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10025", "accno": "1192", "description": "Geleistete Anzahlungen", "link": "AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "13377", "accno": "1200", "description": "Handelsware von <PERSON>", "link": "IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "22000", "accno": "123321", "description": "Test", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "21489", "accno": "12345678901234567890", "description": "Kasse Premium", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10027", "accno": "1280", "description": "Nicht fakturierte Dienstleistungen", "link": "IC", "allow_gl": "true", "is_active": "true"}, {"id": "10029", "accno": "1300", "description": "Bezahlter Aufwand des Folgejahrs", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10030", "accno": "1301", "description": "Noch nicht erhaltener Ertrag", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10031", "accno": "1302", "description": "Arbeitgeberbeitragsreserve", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11849", "accno": "1310", "description": "Aktive Rechnungsabgrenzung Kreditoren", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10034", "accno": "1400", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11093", "accno": "1401", "description": "Obligationen börsen<PERSON>t", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11094", "accno": "1402", "description": "Edelmetalle börsenkotiert", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11095", "accno": "1403", "description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11096", "accno": "1404", "description": "Weitere Fondsanlagen börsenkotiert", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10036", "accno": "1440", "description": "Darlehensforderungen gegenüber Dritten", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10038", "accno": "1450", "description": "Darlehensforderungen gegenüber Beteiligungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10255", "accno": "1460", "description": "Darlehensforderungen gegenüber Beteiligten", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10040", "accno": "1480", "description": "Beteiligungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10042", "accno": "1500", "description": "Maschinen und Apparate", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10043", "accno": "1510", "description": "Mobiliar und Einrichtungen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10044", "accno": "1520", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10045", "accno": "1530", "description": "Fahrzeuge", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10046", "accno": "1540", "description": "Werkzeuge und Geräte", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10047", "accno": "1590", "description": "Übrige mobile Sachanlagen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10049", "accno": "1600", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10051", "accno": "1700", "description": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10052", "accno": "1740", "description": "Selber entwickelte Software", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10053", "accno": "1741", "description": "Erworbene Software", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10054", "accno": "1790", "description": "Übrige immaterielle <PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10257", "accno": "1800", "description": "Nicht einbezahltes Grundkapital", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10058", "accno": "2000", "description": "Verbindlichkeiten aus Lieferungen und Leistungen", "link": "AP", "allow_gl": "false", "is_active": "true"}, {"id": "12519", "accno": "2000.1", "description": "Verbindlichkeiten aus Lieferungen und Leistungen", "link": "AP", "allow_gl": "false", "is_active": "true"}, {"id": "10059", "accno": "2001", "description": "Verbindlichkeiten aus Lieferungen und Leistungen alt", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10060", "accno": "2002", "description": "Verbindlichkeiten für Personalaufwand", "link": "AR_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12412", "accno": "2010", "description": "Verbindlichkeiten aus Lieferungen und Leistungen gegenüber Beteiligten und Organen", "link": "AP", "allow_gl": "false", "is_active": "true"}, {"id": "12413", "accno": "2020", "description": "Verbindlichkeiten aus Lieferungen und Leistungen gegenüber Beteiligungen", "link": "AP", "allow_gl": "false", "is_active": "true"}, {"id": "10674", "accno": "2021", "description": "testnok", "link": "AR_amount:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "10062", "accno": "2030", "description": "Erhaltene Anzahlungen von <PERSON> (Belastung)", "link": "AR_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10545", "accno": "2031", "description": "Erhaltene Anzahlungen von <PERSON> (Artikel)", "link": "IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "22091", "accno": "2040", "description": "TEST", "link": "AR_amount:AP_amount:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "10666", "accno": "2050", "description": "Gutscheine", "link": "AR_amount:AP_amount:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "22125", "accno": "2051", "description": "TEST", "link": "AR_amount:AP_amount:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "10667", "accno": "2055", "description": "Gutscheine eingelöst", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "12669", "accno": "2160", "description": "Kontokorrent Kreditkarte <PERSON>", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10259", "accno": "216000", "description": "Kontokorrent Gesellschafter", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "22176", "accno": "2161", "description": "Kontokorrent <PERSON>", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "11167", "accno": "2166", "description": "Kreditkarte", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "12666", "accno": "21660", "description": "zahlung und löschen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "12160", "accno": "2169", "description": "Kontokorrent Giuseppe", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "13064", "accno": "218011", "description": "Kreditkarte Test Antje", "link": "AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10434", "accno": "2185", "description": "Kreditkarte alt", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10067", "accno": "2200", "description": "Geschuldete MWST", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10284", "accno": "2201", "description": "MWST 8,0%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "21375", "accno": "22010", "description": "MWST 8.1%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10781", "accno": "22012", "description": "MWST 7.7%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10285", "accno": "2202", "description": "MWST 3,8%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10837", "accno": "22020", "description": "MWST 3,7%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "21376", "accno": "22021", "description": "MWST 3.8%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10286", "accno": "2203", "description": "MWST 2,5%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "21377", "accno": "22031", "description": "MWST 2.6%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10287", "accno": "2204", "description": "Bezugsteuer 8,0%", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10794", "accno": "22040", "description": "Bezugsteuer 7,7%", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21378", "accno": "22041", "description": "Bezugsteuer 8.1%", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10288", "accno": "22050", "description": "Befreite Leistungen, Exporte MWST 0% (Ziff. 220)", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10289", "accno": "22051", "description": "Leistungen im Ausland MWST 0% (Ziff. 221)", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10290", "accno": "22052", "description": "Nicht steuerbare Leistungen MWST 0% (Ziff. 230)", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10291", "accno": "22053", "description": "Subventionen MWST 0% (Ziff. 900)", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10292", "accno": "22054", "description": "<PERSON><PERSON>den, Dividenden, Schadenersatz MWST 0% (Ziff. 910)", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10293", "accno": "22055", "description": "Nichtentgelt MWST 0%", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10068", "accno": "2206", "description": "Verrechnungssteuer", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10070", "accno": "2215", "description": "Kreditkarte", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10261", "accno": "2261", "description": "Beschlossene Ausschüttungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10073", "accno": "2270", "description": "Kontokorrent Vorsorgeeinrichtung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10074", "accno": "2271", "description": "Kontokorrent AHV, IV, EO, ALV", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10075", "accno": "2272", "description": "Kontokorrent FAK", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10076", "accno": "2273", "description": "Kontokorrent Unfallversicherung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10077", "accno": "2274", "description": "Kontokorrent Krankentaggeldversicherung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10078", "accno": "2279", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10080", "accno": "2300", "description": "<PERSON>ch nicht bezahlter Aufwand", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10081", "accno": "2301", "description": "Erhaltener Ertrag des Folgejahrs", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "13648", "accno": "2302", "description": "Passive Abgrenzung", "link": "AP", "allow_gl": "false", "is_active": "true"}, {"id": "11850", "accno": "2310", "description": "Passive Rechnungsabgrenzung Kreditoren", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10082", "accno": "2330", "description": "Kurzfristige Rückstellungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10085", "accno": "2400", "description": "Bankverbindlichkeiten langfristig", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10091", "accno": "2600", "description": "Rückstellungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10266", "accno": "2800", "description": "Grundkapital", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10268", "accno": "2900", "description": "Aufgeld (Agio) bei Gründung oder Kapitalerhöhung", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10269", "accno": "2950", "description": "Allgemeine gesetzliche Gewinnreserve", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "12414", "accno": "2951", "description": "Aufwertungsreserve", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "12415", "accno": "2952", "description": "Reserve für eigene Aktien", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10270", "accno": "2960", "description": "Andere Reserven", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "12416", "accno": "2961", "description": "Statutarische und beschlussmässige Gewinnreserven", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10272", "accno": "2970", "description": "Gewinnvortrag oder Verlustvortrag", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10273", "accno": "2979", "description": "Jahresgewinn oder Jahresverlust", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10275", "accno": "2980", "description": "Eigene Aktien", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10094", "accno": "3000", "description": "Produktionserlöse", "link": "AR_amount:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "10096", "accno": "3200", "description": "Handelserlöse", "link": "IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "13804", "accno": "3301", "description": "Room Revenue", "link": "IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "13823", "accno": "3310", "description": "Food Revenue", "link": "", "allow_gl": "false", "is_active": "true"}, {"id": "13808", "accno": "33101", "description": "Bar Revenue", "link": "AR_amount:AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "13824", "accno": "33102", "description": "Self Service Restaurant Revenue", "link": "AR_amount:AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10098", "accno": "3400", "description": "Monatsabos", "link": "AR_amount:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "13283", "accno": "3401", "description": "Energie", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "12467", "accno": "3402", "description": "Dienstunserlös 510", "link": "AR_amount", "allow_gl": "false", "is_active": "true"}, {"id": "13554", "accno": "3403", "description": "Dienstunserlös 510", "link": "AR_amount:IC_sale", "allow_gl": "false", "is_active": "true"}, {"id": "12216", "accno": "3407", "description": "Dienstunserlös 98445", "link": "AR_amount", "allow_gl": "false", "is_active": "true"}, {"id": "12646", "accno": "3408", "description": "Dienstunserlö<PERSON>", "link": "AR_amount", "allow_gl": "false", "is_active": "true"}, {"id": "11003", "accno": "3410", "description": "Dienstleistungserlöse 10", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "10680", "accno": "3497", "description": "MWST 8,0% Saldo", "link": "AR_tax:IC_taxpart:IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "13784", "accno": "3500", "description": "Bar Revenue", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "10100", "accno": "3600", "description": "Nebenerlöse aus Lieferungen und Leistungen", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "10101", "accno": "3680", "description": "Sonstige Erlöse", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "10102", "accno": "3689", "description": "<PERSON><PERSON> kont<PERSON>t", "link": "AR_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10103", "accno": "3699", "description": "Systemkonto", "link": "AR_amount:IC_sale:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "10105", "accno": "3700", "description": "Eigenleistungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10106", "accno": "3710", "description": "Eigenverbrauch", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10108", "accno": "3800", "description": "<PERSON><PERSON><PERSON>", "link": "AR_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10109", "accno": "3801", "description": "Rabatte und Preisnachlässe", "link": "AR_paid", "allow_gl": "true", "is_active": "true"}, {"id": "21649", "accno": "38011", "description": "Rabatte", "link": "AR_amount", "allow_gl": "true", "is_active": "true"}, {"id": "21650", "accno": "38012", "description": "Rundungsdifferenzen", "link": "AR_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10110", "accno": "3804", "description": "Inkassospesen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10111", "accno": "3805", "description": "Verluste Forderungen, Veränderung Wertberichtigungen", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "14001", "accno": "3807", "description": "Gebühren Sumup", "link": "AR_amount:IC_income", "allow_gl": "true", "is_active": "true"}, {"id": "10113", "accno": "3900", "description": "Bestandesänderungen unfertige Erzeugnisse", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10114", "accno": "3901", "description": "Bestandesänderungen fertige Erzeugnisse", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10115", "accno": "3904", "description": "Bestandesänderungen nicht fakturierte Dienstleistungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10762", "accno": "4000", "description": "<PERSON><PERSON><PERSON> nichts", "link": "IC_taxservice", "allow_gl": "true", "is_active": "true"}, {"id": "10964", "accno": "4000.dot.test", "description": "<PERSON><PERSON><PERSON> nichts", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10963", "accno": "4000-minus-test", "description": "<PERSON><PERSON><PERSON> nichts", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10962", "accno": "4000_underline_test", "description": "<PERSON><PERSON><PERSON> nichts", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10874", "accno": "4001", "description": "<PERSON><PERSON><PERSON>n", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10764", "accno": "4010", "description": "<PERSON><PERSON><PERSON> garnichts", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10120", "accno": "4200", "description": "Einkauf Handelsware", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10763", "accno": "4220", "description": "Einkauf Handelsware 2", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "11910", "accno": "4300", "description": "Test Rot", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "11911", "accno": "43000", "description": "Test Rot", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10122", "accno": "4400", "description": "Einkauf Drittleistung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10127", "accno": "4800", "description": "Bestandesänderungen Handelswaren", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10128", "accno": "4880", "description": "Materialverluste", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10130", "accno": "4999", "description": "Systemkonto", "link": "AP_amount:IC_expense:IC_cogs", "allow_gl": "true", "is_active": "true"}, {"id": "10132", "accno": "5400", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10133", "accno": "5401", "description": "Zulagen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10134", "accno": "5402", "description": "Erfolgsbeteiligungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10135", "accno": "5403", "description": "Provisionen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10136", "accno": "5405", "description": "Leistungen von Sozialversicherungen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10138", "accno": "5700", "description": "AHV, IV, EO, ALV", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10139", "accno": "5710", "description": "FAK", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10140", "accno": "5720", "description": "Vorsorgeeinrichtungen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10141", "accno": "5730", "description": "Unfallversicherung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10142", "accno": "5740", "description": "Krankentaggeldversicherung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10143", "accno": "5790", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10145", "accno": "5800", "description": "Personalinserate", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10146", "accno": "5810", "description": "Aus- und Weiterbildung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10147", "accno": "5820", "description": "Spesenentschädigung effektiv", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10148", "accno": "5830", "description": "Spesenentschädigung pauschal", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10149", "accno": "5840", "description": "<PERSON><PERSON><PERSON>", "link": "AP_amount:IC_expense", "allow_gl": "true", "is_active": "true"}, {"id": "10150", "accno": "5880", "description": "<PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "21346", "accno": "5888", "description": "Super Personalaufwand", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10151", "accno": "5890", "description": "Privatanteile <PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10153", "accno": "5900", "description": "Leistungen Dritter", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10156", "accno": "6000", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10833", "accno": "6010", "description": "Mieten Parkplatz", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10157", "accno": "6030", "description": "<PERSON><PERSON><PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10158", "accno": "6040", "description": "Reinigung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10159", "accno": "6090", "description": "Privat<PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10161", "accno": "6130", "description": "URE Büromobiliar", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10162", "accno": "6160", "description": "Leasing mobile Sachanlagen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10164", "accno": "6200", "description": "Reparaturen, Service, Reinigung Fahrzeuge", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "13375", "accno": "6202", "description": "<PERSON><PERSON> du<PERSON>u", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "12217", "accno": "6203", "description": "TEST TEST", "link": "AP_amount:IC_income:IC_sale", "allow_gl": "false", "is_active": "true"}, {"id": "12647", "accno": "6204", "description": "Baum", "link": "AP_amount:IC_income:IC_sale", "allow_gl": "false", "is_active": "true"}, {"id": "13555", "accno": "6205", "description": "<PERSON><PERSON> du<PERSON>u", "link": "AP_amount", "allow_gl": "false", "is_active": "true"}, {"id": "10165", "accno": "6210", "description": "Betriebsstoffe Fahrzeuge", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10166", "accno": "6220", "description": "Versicherungen Fahrzeuge", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10167", "accno": "6230", "description": "Verkehrsabgaben, Beiträge, Gebühren", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10168", "accno": "6260", "description": "Fahrzeugleasing, Fahrzeugmieten", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10169", "accno": "6270", "description": "Privatanteil Fahrzeugaufwand", "link": "AR_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10170", "accno": "6280", "description": "Transportaufwand", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10172", "accno": "6300", "description": "Sachversicherungen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10173", "accno": "6360", "description": "Abgaben und Gebühren", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10174", "accno": "6370", "description": "Bewilligungen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10176", "accno": "6400", "description": "Elektrizität", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10177", "accno": "6460", "description": "Entsorgungsaufwand", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10180", "accno": "6500", "description": "Büromaterial", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "12667", "accno": "65000", "description": "buchung und löschen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10181", "accno": "6501", "description": "Drucksachen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10182", "accno": "6503", "description": "Fachliteratur", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10183", "accno": "6510", "description": "Telekommunikation", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10184", "accno": "6513", "description": "Porti", "link": "AP_amount:IC_expense", "allow_gl": "true", "is_active": "true"}, {"id": "10185", "accno": "6520", "description": "Be<PERSON>räge, Spenden, Vergabungen, Trinkgelder", "link": "AP_amount:IC_expense", "allow_gl": "true", "is_active": "true"}, {"id": "10186", "accno": "6530", "description": "Buchführung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10187", "accno": "6531", "description": "Unternehmensberatung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10188", "accno": "6532", "description": "Rechtsberatung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10189", "accno": "6540", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Generalversammlung", "link": "AP_amount:IC_expense", "allow_gl": "true", "is_active": "true"}, {"id": "10190", "accno": "6542", "description": "Revisionsstelle", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10191", "accno": "6550", "description": "Gründungs-, Kapitalerhöhungs- und Organisationsaufwand", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10192", "accno": "6551", "description": "Inkasso- und Betreibungsaufwand", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10194", "accno": "6570", "description": "Leasing Hardware und Software", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10195", "accno": "6580", "description": "<PERSON><PERSON><PERSON>, Updates", "link": "AP_amount:IC_expense", "allow_gl": "true", "is_active": "true"}, {"id": "10196", "accno": "6581", "description": "Hosting und Wartung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10197", "accno": "6583", "description": "Verbrauchsmaterial IT", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10198", "accno": "6590", "description": "Beratung und Entwicklung IT", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10200", "accno": "6600", "description": "Werbeinserate", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10201", "accno": "6610", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10202", "accno": "6620", "description": "Fachmessen, Ausstellungen, Dekoration", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10203", "accno": "6640", "description": "Reisespesen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10204", "accno": "6641", "description": "Kundenbetreuung", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10205", "accno": "6660", "description": "Sponsoring", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10206", "accno": "6670", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>itsarbeit, Public Relations", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10207", "accno": "6680", "description": "<PERSON><PERSON><PERSON><PERSON>tung, Marktanalysen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10209", "accno": "6700", "description": "Sonstiger betrieblicher Aufwand", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10836", "accno": "6730", "description": "Vorsteuerkorrektur", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "21312", "accno": "67300_inaktivab010121", "description": "Vorsteuerkorrektur alt", "link": "AP_tax", "allow_gl": "true", "is_active": "true"}, {"id": "10210", "accno": "6799", "description": "<PERSON><PERSON> kont<PERSON>t", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10212", "accno": "6800", "description": "Wertberichtigungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10213", "accno": "6820", "description": "Abschreibungen", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10215", "accno": "6900", "description": "Zinsaufwand gegenüber Dritten", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10276", "accno": "6920", "description": "Zinsaufwand gegenüber Beteiligten und Organen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10216", "accno": "6930", "description": "Zinsaufwand gegenüber Vorsorgeeinrichtungen", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10217", "accno": "6940", "description": "Bankspesen", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10218", "accno": "6942", "description": "Kursverluste", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10219", "accno": "6943", "description": "Kreditkartengebühr", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10220", "accno": "6944", "description": "Bankspesen Zahlungsverkehr, Rundungsdifferenzen", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}, {"id": "10222", "accno": "6950", "description": "Erträge aus flüssigen Mitteln und Wertschriften", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10223", "accno": "6952", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11100", "accno": "6990", "description": "Aufwände aus Wertschriften des Anlagevermögens (nicht realisierte Erfolge)", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "11101", "accno": "6991", "description": "Erträge aus Wertschriften des Anlagevermögens (nicht realisierte Erfolge)", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10226", "accno": "7000", "description": "Ertrag aus Nebenbetrieb", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10228", "accno": "7010", "description": "Aufwand aus Nebenbetrieb", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10231", "accno": "8000", "description": "Betriebsfremder Aufwand", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10233", "accno": "8100", "description": "Betriebsfremder Ertrag", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10235", "accno": "8500", "description": "Ausserordentlicher Aufwand", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10237", "accno": "8510", "description": "Ausserordentlicher Ertrag", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10239", "accno": "8600", "description": "<PERSON><PERSON><PERSON>", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10241", "accno": "8610", "description": "Einmaliger Ertrag", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10243", "accno": "8700", "description": "Periodenfremder Aufwand", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10245", "accno": "8710", "description": "Periodenfremder Ertrag", "link": "", "allow_gl": "true", "is_active": "true"}, {"id": "10118", "accno": "8888", "description": "Testkonto8", "link": "AR_paid:AP_paid:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "10505", "accno": "8889", "description": "Testkonto9", "link": "AR_paid:AP_paid:IC_income:IC_sale", "allow_gl": "true", "is_active": "true"}, {"id": "10247", "accno": "8900", "description": "Kantons- und Gemeindesteuern", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "10248", "accno": "8901", "description": "Direkte Bundessteuern", "link": "AP_amount", "allow_gl": "true", "is_active": "true"}, {"id": "12581", "accno": "CH63 007001147510 121211", "description": "UBS", "link": "AR_paid:AP_paid", "allow_gl": "true", "is_active": "true"}]