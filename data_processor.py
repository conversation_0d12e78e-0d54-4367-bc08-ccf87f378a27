import logging
import re
from typing import Dict, List, Any, Tuple, Optional
from decimal import Decimal, ROUND_HALF_UP
from collections import defaultdict


class DataProcessor:
    """Verarbeitet API-Daten und erstellt die gestufte Erfolgsrechnung"""

    def __init__(self):
        # Kontokategorien gemäß readme.md
        self.account_categories = {
            'ertrag': {'prefix': '3', 'name': 'Ertrag'},
            'direkter_aufwand': {'prefix': '4', 'name': 'Direk<PERSON>fwand'},
            'personalaufwand': {'prefix': '5', 'name': 'Personalaufwand'},
            'betrieblicher_aufwand': {'prefix': '6', 'name': 'Betrieblicher Aufwand'},
            'betrieblicher_nebenerfolg': {'prefix': '7', 'name': 'Betrieblicher Nebenerfolg'},
            'periodenfremder_erfolg': {'prefix': '8', 'name': 'Periodenfremder und ausserordentlicher Erfolg'},
            'abschreibungen': {'prefix': '68', 'name': 'Abschreibungen'},
            'finanzerfolg': {'prefix': '69', 'name': 'Finanzerfolg'},
            'steuern': {'prefix': '89', 'name': 'Steuern'}
        }

        # ER-Gliederung gemäß readme.md
        self.er_structure = [
            {'type': 'category', 'key': 'ertrag', 'name': 'Ertrag'},
            {'type': 'total', 'key': 'total_ertrag', 'name': 'Total Ertrag'},
            {'type': 'category', 'key': 'direkter_aufwand',
                'name': 'Direkter Aufwand'},
            {'type': 'total', 'key': 'total_direkter_aufwand',
                'name': 'Total Direkter Aufwand'},
            {'type': 'calculation', 'key': 'bruttoergebnis_1',
                'name': 'Bruttoergebnis 1'},
            {'type': 'category', 'key': 'personalaufwand', 'name': 'Personalaufwand'},
            {'type': 'total', 'key': 'total_personalaufwand',
                'name': 'Total Personalaufwand'},
            {'type': 'calculation', 'key': 'bruttoergebnis_2',
                'name': 'Bruttoergebnis 2'},
            {'type': 'category', 'key': 'betrieblicher_aufwand',
                'name': 'Betrieblicher Aufwand'},
            {'type': 'total', 'key': 'total_betrieblicher_aufwand',
                'name': 'Total Betrieblicher Aufwand'},
            {'type': 'calculation', 'key': 'betriebsergebnis_1',
                'name': 'Betriebsergebnis 1 (EBITDA)'},
            {'type': 'category', 'key': 'abschreibungen', 'name': 'Abschreibungen'},
            {'type': 'total', 'key': 'total_abschreibungen',
                'name': 'Total Abschreibungen'},
            {'type': 'calculation', 'key': 'betriebsergebnis_2',
                'name': 'Betriebsergebnis 2 (EBIT)'},
            {'type': 'category', 'key': 'finanzerfolg', 'name': 'Finanzerfolg'},
            {'type': 'total', 'key': 'total_finanzerfolg',
                'name': 'Total Finanzerfolg'},
            {'type': 'category', 'key': 'betrieblicher_nebenerfolg',
                'name': 'Betrieblicher Nebenerfolg'},
            {'type': 'total', 'key': 'total_betrieblicher_nebenerfolg',
                'name': 'Total betrieblicher Nebenerfolg'},
            {'type': 'category', 'key': 'periodenfremder_erfolg',
                'name': 'Periodenfremder und ausserordentlicher Erfolg'},
            {'type': 'total', 'key': 'total_periodenfremder_erfolg',
                'name': 'Total periodenfremder und ausserordentlicher Erfolg'},
            {'type': 'calculation', 'key': 'betriebsergebnis_3',
                'name': 'Betriebsergebnis 3 (EBT)'},
            {'type': 'category', 'key': 'steuern', 'name': 'Steuern'},
            {'type': 'total', 'key': 'total_steuern', 'name': 'Total Steuern'},
            {'type': 'calculation', 'key': 'total_aufwand', 'name': 'Total Aufwand'},
            {'type': 'calculation', 'key': 'erfolg', 'name': 'Erfolg'}
        ]

    def categorize_account(self, account_number: str) -> Optional[str]:
        """
        Kategorisiert ein Konto basierend auf der Kontonummer

        Args:
            account_number: Kontonummer als String

        Returns:
            Kategorie-Key oder None wenn nicht relevant
        """
        if not account_number:
            return None

        # Spezielle Fälle zuerst prüfen
        if account_number.startswith('68'):
            return 'abschreibungen'
        elif account_number.startswith('69'):
            return 'finanzerfolg'
        elif account_number.startswith('89'):
            return 'steuern'

        # Standard-Kategorien
        first_digit = account_number[0]
        for category, info in self.account_categories.items():
            if info['prefix'] == first_digit:
                return category

        # Konten mit 1 oder 2 sind nicht relevant für ER
        if first_digit in ['1', '2']:
            return None

        return None

    def _sort_account_key(self, account_number: str) -> Tuple:
        """
        Erstellt einen Sortierungsschlüssel für Kontonummern

        Behandelt alphanumerische Kontonummern wie:
        - 6581
        - 6581.1
        - 6581.3
        - 6583
        - 6583A

        Args:
            account_number: Kontonummer als String

        Returns:
            Tuple für Sortierung
        """
        if not account_number:
            return (0,)

        # Kontonummer in Teile zerlegen (Zahlen und Buchstaben getrennt)
        parts = []
        current_part = ""
        current_is_digit = None

        for char in account_number:
            char_is_digit = char.isdigit()

            if current_is_digit is None:
                # Erster Charakter
                current_is_digit = char_is_digit
                current_part = char
            elif current_is_digit == char_is_digit:
                # Gleicher Typ wie vorheriger Charakter
                current_part += char
            else:
                # Typ wechselt - aktuellen Teil speichern und neuen beginnen
                if current_is_digit:
                    parts.append(int(current_part))
                else:
                    parts.append(current_part)
                current_part = char
                current_is_digit = char_is_digit

        # Letzten Teil hinzufügen
        if current_part:
            if current_is_digit:
                parts.append(int(current_part))
            else:
                parts.append(current_part)

        return tuple(parts)

    def get_sorted_accounts_for_category(self, er_data: Dict[str, Any], category: str) -> List[Tuple[str, Dict]]:
        """
        Gibt sortierte Konten für eine Kategorie zurück

        Args:
            er_data: ER-Datenstruktur
            category: Kategorie-Key

        Returns:
            Liste von (account_number, account_data) Tupeln, sortiert
        """
        category_accounts = []
        for account_number, account_data in er_data['accounts'].items():
            if account_data['category'] == category:
                category_accounts.append((account_number, account_data))

        # Nach Kontonummer sortieren
        category_accounts.sort(key=lambda x: self._sort_account_key(x[0]))
        return category_accounts

    def process_transactions(self, accounts: List[Dict], transactions: Dict[str, List[Dict]], departments: List[Dict]) -> Dict[str, Any]:
        """
        Verarbeitet Buchungen und erstellt die ER-Struktur

        Args:
            accounts: Liste der Konten
            transactions: Dictionary mit Buchungen pro Konto
            departments: Liste der Kostenstellen

        Returns:
            ER-Datenstruktur
        """
        logging.info("Verarbeite Buchungen für ER...")

        # Kostenstellen-Mapping aus Buchungen extrahieren
        dept_mapping = {}

        # Debug: Kostenstellen-Extraktion verfolgen
        debug_departments = []

        # Erst alle Buchungen durchgehen, um Kostenstellen zu sammeln
        for account_number, account_transactions in transactions.items():
            for transaction in account_transactions:
                dept_info = transaction.get('department', {})
                dept_id = str(dept_info.get('id', '0'))
                # Für Kostenstelle 0 (keine Kostenstelle) einen besseren Namen verwenden
                if dept_id == '0':
                    dept_name = "ohne Kostenstelle"
                else:
                    dept_name = dept_info.get(
                        'name', f"Kostenstelle {dept_id}")

                # Debug: Jede gefundene Kostenstelle protokollieren
                debug_departments.append({
                    'account': account_number,
                    'transaction_id': transaction.get('id'),
                    'department_raw': dept_info,
                    'dept_id': dept_id,
                    'dept_name': dept_name
                })

                if dept_id and dept_id not in dept_mapping:
                    dept_mapping[dept_id] = dept_name

        # Debug: Kostenstellen-Analyse speichern
        import json
        with open('debug_departments_extraction.json', 'w', encoding='utf-8') as f:
            json.dump(debug_departments, f, indent=2, ensure_ascii=False)

        logging.info(
            f"Debug: Kostenstellen-Extraktion - {len(debug_departments)} Buchungen analysiert, {len(dept_mapping)} Kostenstellen gefunden")
        for dept_id, dept_name in dept_mapping.items():
            logging.info(
                f"Debug: Kostenstelle gefunden - ID: {dept_id}, Name: {dept_name}")

        # Konten-Mapping erstellen
        account_mapping = {}
        for account in accounts:
            acc_no = account.get('accno')
            acc_name = account.get('description', f"Konto {acc_no}")
            if acc_no:
                account_mapping[acc_no] = {
                    'name': acc_name,
                    'category': self.categorize_account(acc_no)
                }

        # Datenstruktur für ER initialisieren
        er_data = {
            'departments': dept_mapping,
            'accounts': {},
            'totals': {},
            'calculations': {}
        }

        # Buchungen verarbeiten
        for account_number, account_transactions in transactions.items():
            if account_number not in account_mapping:
                continue

            account_info = account_mapping[account_number]
            category = account_info['category']

            if not category:  # Konto nicht relevant für ER
                continue

            # Saldos pro Kostenstelle berechnen
            dept_saldos = defaultdict(Decimal)

            for transaction in account_transactions:
                amount = self._parse_amount(transaction.get('amount', 0))
                dept_info = transaction.get('department', {})
                dept_id = str(dept_info.get('id', '0'))  # Default-Kostenstelle

                dept_saldos[dept_id] += amount

            # Konto zu ER-Daten hinzufügen
            er_data['accounts'][account_number] = {
                'name': account_info['name'],
                'category': category,
                'department_saldos': dict(dept_saldos)
            }

        # Totals und Berechnungen durchführen
        self._calculate_totals_and_results(er_data)

        logging.info("ER-Verarbeitung abgeschlossen")
        return er_data

    def _parse_amount(self, amount: Any) -> Decimal:
        """
        Parst einen Betrag zu Decimal

        Args:
            amount: Betrag als String, Float oder Decimal

        Returns:
            Decimal-Betrag
        """
        if isinstance(amount, Decimal):
            return amount
        elif isinstance(amount, (int, float)):
            return Decimal(str(amount))
        elif isinstance(amount, str):
            try:
                return Decimal(amount.replace(',', '.'))
            except:
                return Decimal('0')
        else:
            return Decimal('0')

    def _calculate_totals_and_results(self, er_data: Dict[str, Any]):
        """
        Berechnet Totals und Zwischenergebnisse

        Args:
            er_data: ER-Datenstruktur (wird modifiziert)
        """
        departments = list(er_data['departments'].keys())

        # Totals pro Kategorie berechnen
        for category in self.account_categories.keys():
            category_total = defaultdict(Decimal)

            for account_number, account_data in er_data['accounts'].items():
                if account_data['category'] == category:
                    for dept_id, saldo in account_data['department_saldos'].items():
                        category_total[dept_id] += saldo

            er_data['totals'][f'total_{category}'] = dict(category_total)

        # Berechnungen durchführen
        calculations = er_data['calculations']
        totals = er_data['totals']

        # Bruttoergebnis 1 = Ertrag + Direkter Aufwand (Aufwand ist bereits negativ)
        calculations['bruttoergebnis_1'] = self._add_depts(
            totals.get('total_ertrag', {}),
            totals.get('total_direkter_aufwand', {})
        )

        # Bruttoergebnis 2 = Bruttoergebnis 1 + Personalaufwand (Aufwand ist bereits negativ)
        calculations['bruttoergebnis_2'] = self._add_depts(
            calculations['bruttoergebnis_1'],
            totals.get('total_personalaufwand', {})
        )

        # Betriebsergebnis 1 (EBITDA) = Bruttoergebnis 2 + Betrieblicher Aufwand (Aufwand ist bereits negativ)
        calculations['betriebsergebnis_1'] = self._add_depts(
            calculations['bruttoergebnis_2'],
            totals.get('total_betrieblicher_aufwand', {})
        )

        # Betriebsergebnis 2 (EBIT) = Betriebsergebnis 1 + Abschreibungen (Aufwand ist bereits negativ)
        calculations['betriebsergebnis_2'] = self._add_depts(
            calculations['betriebsergebnis_1'],
            totals.get('total_abschreibungen', {})
        )

        # Betriebsergebnis 3 (EBT) = Betriebsergebnis 2 + Finanzerfolg + Betrieblicher Nebenerfolg + Periodenfremder Erfolg
        temp_result = self._add_depts(
            calculations['betriebsergebnis_2'], totals.get('total_finanzerfolg', {}))
        temp_result = self._add_depts(temp_result, totals.get(
            'total_betrieblicher_nebenerfolg', {}))
        calculations['betriebsergebnis_3'] = self._add_depts(
            temp_result, totals.get('total_periodenfremder_erfolg', {}))

        # Total Aufwand = Alle Aufwandskonten (inkl. Erfolgskonten)
        total_aufwand = defaultdict(Decimal)
        aufwand_categories = ['direkter_aufwand', 'personalaufwand',
                              'betrieblicher_aufwand', 'abschreibungen', 'steuern',
                              'finanzerfolg', 'betrieblicher_nebenerfolg', 'periodenfremder_erfolg']

        # Alle Aufwandskategorien (inkl. Erfolgskonten)
        for category in aufwand_categories:
            for dept_id, amount in totals.get(f'total_{category}', {}).items():
                total_aufwand[dept_id] += amount

        calculations['total_aufwand'] = dict(total_aufwand)

        # Erfolg = Total Ertrag + Total Aufwand (Total Aufwand enthält bereits alle Aufwandskonten)
        calculations['erfolg'] = self._add_depts(
            totals.get('total_ertrag', {}),
            calculations['total_aufwand']
        )

    def _add_depts(self, dept_dict1: Dict[str, Decimal], dept_dict2: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """Addiert zwei Kostenstellen-Dictionaries"""
        result = defaultdict(Decimal)
        for dept_id, amount in dept_dict1.items():
            result[dept_id] += amount
        for dept_id, amount in dept_dict2.items():
            result[dept_id] += amount
        return dict(result)

    def _subtract_depts(self, dept_dict1: Dict[str, Decimal], dept_dict2: Dict[str, Decimal]) -> Dict[str, Decimal]:
        """Subtrahiert zwei Kostenstellen-Dictionaries"""
        result = defaultdict(Decimal)
        for dept_id, amount in dept_dict1.items():
            result[dept_id] += amount
        for dept_id, amount in dept_dict2.items():
            result[dept_id] -= amount
        return dict(result)
