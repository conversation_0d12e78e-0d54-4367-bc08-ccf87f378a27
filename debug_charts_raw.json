{"chart": [{"@id": "10004", "@accno": "1000", "@description": "<PERSON><PERSON>", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@symbol_link": "kassa", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13289", "@accno": "10001", "@description": "Kasse - Energie", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "22139", "@accno": "10002", "@description": "Kasse_TEST", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11113", "@accno": "1010", "@description": "Post CHF CH88 0900 0000 1506 3577 9 (1010)", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12644", "@accno": "1011", "@description": "POST CHF ********************* (1011)", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "bank_account_settings": {"@bank_name": "POST", "@iban": "*********************", "@bic": "POFICHBEXXX", "@currency": "CHF"}, "taxes": null}, {"@id": "10005", "@accno": "1020", "@description": "Aareal Bank AG EUR ********************* (1020)", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "bank_account_settings": {"@bank_name": "ZKB", "@iban": "*********************", "@bic": "ZKBKCHZZ80A", "@currency": "CHF"}, "taxes": null}, {"@id": "14007", "@accno": "1021", "@description": "ZKB CAD CH63 0070 0114 5000 5131 6 (1022)", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12370", "@accno": "1022", "@description": "UBS CHF CH63 0070 0114 5000 5131 6 (1020)", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12465", "@accno": "1023", "@description": "UBS CH63 0070 0114 7510 5445 1", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13552", "@accno": "1024", "@description": "UBS CH64 0070 0114 7510 121211", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "100", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12252", "@accno": "1026", "@description": "USD-Bankkonto", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12226", "@accno": "1027", "@description": "ZKB CH63 0070 0114 7510 68s6s7dg6", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12718", "@accno": "1028", "@description": "UBS EUR CH12 3456 7890 1234 5678 9 (1028)", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10704", "@accno": "1030", "@description": "<PERSON><PERSON>", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12374", "@accno": "1060", "@description": "Wertschriften mit Börsenkurs", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "106", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10008", "@accno": "1090", "@description": "Transferkonto", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "109", "@symbol_link": "transfer", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13002", "@accno": "1098", "@description": "<PERSON><PERSON><PERSON> Beträ<PERSON>", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "109", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10009", "@accno": "1099", "@description": "Unklare Beträge", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "109", "@symbol_link": "abklärung", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10011", "@accno": "1100", "@description": "Forderungen Schweiz", "@category": "A", "@link": "AR", "@charttype": "A", "@gifi": "110", "@symbol_link": "forderungenschweiz", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "22138", "@accno": "11001", "@description": "Forderungen Schweiz_TEST", "@category": "A", "@link": "AR", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10012", "@accno": "1101", "@description": "Forderungen Ausland", "@category": "A", "@link": "AR", "@charttype": "A", "@gifi": "110", "@symbol_link": "forderungenauslandchf", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10013", "@accno": "1102", "@description": "Forderungen Ausland Fremdwährungen", "@category": "A", "@link": "AR", "@charttype": "A", "@gifi": "110", "@symbol_link": "forderungenauslandandere", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10014", "@accno": "1103", "@description": "Forderungen alt", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13843", "@accno": "1104", "@description": "Forderungen - Test FW-Bewertung MERC", "@category": "A", "@link": "AR", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "14000", "@accno": "1107", "@description": "Durchlaufkonto SumUp", "@category": "A", "@link": "AR", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "14005", "@accno": "1107.1", "@description": "Durchlaufkonto Sumup", "@category": "A", "@link": "AR_paid", "@charttype": "A", "@gifi": "110", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10713", "@accno": "11080", "@description": "Durchlaufkonto Mobiliar", "@category": "A", "@link": "AR_paid", "@charttype": "A", "@gifi": "110", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10015", "@accno": "1109", "@description": "Wertberichtigungen Forderungen aus Lieferungen und Leistungen gegenüber Dritten", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10817", "@accno": "11090", "@description": "Durchlaufkonto Steffi", "@category": "A", "@link": "AR_amount", "@charttype": "A", "@gifi": "110", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12366", "@accno": "11091", "@description": "Durchlaufkonto Hallo", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12466", "@accno": "11092", "@description": "Durchlaufkonto Hallo", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "13553", "@accno": "11094", "@description": "Durchlaufkonto Hallo Test", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12215", "@accno": "11095", "@description": "Durchlaufkonto TEST", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "13373", "@accno": "11096", "@description": "Durchlaufkonto Stripe", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "110", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10017", "@accno": "1110", "@description": "Forderungen aus Lieferungen und Leistungen gegenüber Beteiligungen", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "111", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13001", "@accno": "1111", "@description": "test pascal", "@category": "A", "@link": "AR_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10252", "@accno": "1120", "@description": "Forderungen aus Lieferungen und Leistungen gegenüber Beteiligten", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "112", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10278", "@accno": "1170", "@description": "Vorsteuer 8.0% auf Mat. + DL", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.08", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.08", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10788", "@accno": "11700", "@description": "Vorsteuer 7.7% auf Mat. + DL", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.077", "@charttype": "A", "@gifi": "117", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21369", "@accno": "11701", "@description": "Vorsteuer 8.1% auf Mat. + DL", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.081", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.081", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10879", "@accno": "11710", "@description": "Zoll auf Mat. + DL", "@category": "A", "@link": "AP_tax", "@tax_rate": "1", "@charttype": "A", "@gifi": "117", "@symbol_link": "zollkonto1", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "1.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10880", "@accno": "11711", "@description": "Zoll auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "1", "@charttype": "A", "@gifi": "117", "@symbol_link": "zollkonto2", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "1.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10279", "@accno": "1172", "@description": "Vorsteuer 2.5% auf Mat. + DL", "@category": "A", "@link": "AP_tax:IC_taxservice", "@tax_rate": "0.025", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.025", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21370", "@accno": "11721", "@description": "Vorsteuer 2.6% auf Mat. + DL", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.026", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.026", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10280", "@accno": "1173", "@description": "Vorsteuer 7.7% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax:IC_taxservice", "@tax_rate": "0.077", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10789", "@accno": "11730", "@description": "Vorsteuer 7.7% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.077", "@charttype": "A", "@gifi": "117", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21371", "@accno": "11731", "@description": "Vorsteuer 8.1% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.081", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.081", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10281", "@accno": "1174", "@description": "Vorsteuer 3.8% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.038", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.038", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10790", "@accno": "11740", "@description": "Vorsteuer 3.7% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.037", "@charttype": "A", "@gifi": "117", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.037", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21372", "@accno": "11741", "@description": "Vorsteuer 3.8% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.038", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.038", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10282", "@accno": "1175", "@description": "Vorsteuer 2.5% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.025", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.025", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "12371", "@accno": "11750", "@description": "Vorsteuer 1.1% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.011", "@charttype": "A", "@gifi": "117", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.011", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21373", "@accno": "11751", "@description": "Vorsteuer 2.6% auf Inv. + übr. BA", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.026", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.026", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10283", "@accno": "1176", "@description": "Vorsteuer 8,0% Bezugsteuer", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.08", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.08", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10791", "@accno": "11760", "@description": "Vorsteuer 7,7% Bezugssteuer", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.077", "@charttype": "A", "@gifi": "117", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21374", "@accno": "11761", "@description": "Vorsteuer 8.1% Bezugsteuer", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.081", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.081", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10021", "@accno": "1177", "@description": "Forderungen gegenüber Oberzolldirektion", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "117", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "22189", "@accno": "11779", "@description": "Leistungen im Ausland und Leistungen nach Art.90", "@category": "A", "@link": "AR_tax:AP_tax:IC_taxpart", "@tax_rate": "0", "@charttype": "A", "@gifi": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10641", "@accno": "1178", "@description": "Vorsteuer Test 5.0%", "@category": "A", "@link": "AP_tax", "@tax_rate": "0.05", "@charttype": "A", "@gifi": "117", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.05", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10022", "@accno": "1179", "@description": "Verrechnungssteuer", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "117", "@symbol_link": "verrech<PERSON>ngssteuer", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10025", "@accno": "1192", "@description": "Geleistete Anzahlungen", "@category": "A", "@link": "AP_paid", "@charttype": "A", "@gifi": "109", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13377", "@accno": "1200", "@description": "Handelsware von <PERSON>", "@category": "A", "@link": "IC_income:IC_sale", "@charttype": "A", "@gifi": "120", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "22000", "@accno": "123321", "@description": "Test", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "100", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "21489", "@accno": "12345678901234567890", "@description": "Kasse Premium", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10027", "@accno": "1280", "@description": "Nicht fakturierte Dienstleistungen", "@category": "A", "@link": "IC", "@charttype": "A", "@gifi": "120", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10029", "@accno": "1300", "@description": "Bezahlter Aufwand des Folgejahrs", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "130", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10030", "@accno": "1301", "@description": "Noch nicht erhaltener Ertrag", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "130", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10031", "@accno": "1302", "@description": "Arbeitgeberbeitragsreserve", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "130", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11849", "@accno": "1310", "@description": "Aktive Rechnungsabgrenzung Kreditoren", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "130", "@symbol_link": "aktiverechnungsabgrenzung", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10034", "@accno": "1400", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "140", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11093", "@accno": "1401", "@description": "Obligationen börsen<PERSON>t", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "140", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11094", "@accno": "1402", "@description": "Edelmetalle börsenkotiert", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "140", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11095", "@accno": "1403", "@description": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "140", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11096", "@accno": "1404", "@description": "Weitere Fondsanlagen börsenkotiert", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "140", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10036", "@accno": "1440", "@description": "Darlehensforderungen gegenüber Dritten", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "144", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10038", "@accno": "1450", "@description": "Darlehensforderungen gegenüber Beteiligungen", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "145", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10255", "@accno": "1460", "@description": "Darlehensforderungen gegenüber Beteiligten", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "146", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10040", "@accno": "1480", "@description": "Beteiligungen", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "148", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10042", "@accno": "1500", "@description": "Maschinen und Apparate", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "150", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10043", "@accno": "1510", "@description": "Mobiliar und Einrichtungen", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "150", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10044", "@accno": "1520", "@description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "150", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10045", "@accno": "1530", "@description": "Fahrzeuge", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "150", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10046", "@accno": "1540", "@description": "Werkzeuge und Geräte", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "150", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10047", "@accno": "1590", "@description": "Übrige mobile Sachanlagen", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "150", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10049", "@accno": "1600", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "160", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10051", "@accno": "1700", "@description": "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "170", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10052", "@accno": "1740", "@description": "Selber entwickelte Software", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "170", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10053", "@accno": "1741", "@description": "Erworbene Software", "@category": "A", "@link": "AP_amount", "@charttype": "A", "@gifi": "170", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10054", "@accno": "1790", "@description": "Übrige immaterielle <PERSON>", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "170", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10257", "@accno": "1800", "@description": "Nicht einbezahltes Grundkapital", "@category": "A", "@link": "", "@charttype": "A", "@gifi": "180", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10058", "@accno": "2000", "@description": "Verbindlichkeiten aus Lieferungen und Leistungen", "@category": "L", "@link": "AP", "@charttype": "A", "@gifi": "200", "@symbol_link": "", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12519", "@accno": "2000.1", "@description": "Verbindlichkeiten aus Lieferungen und Leistungen", "@category": "L", "@link": "AP", "@charttype": "A", "@gifi": "200", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10059", "@accno": "2001", "@description": "Verbindlichkeiten aus Lieferungen und Leistungen alt", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "200", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10060", "@accno": "2002", "@description": "Verbindlichkeiten für Personalaufwand", "@category": "L", "@link": "AR_paid", "@charttype": "A", "@gifi": "200", "@symbol_link": "lohnzahlung", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12412", "@accno": "2010", "@description": "Verbindlichkeiten aus Lieferungen und Leistungen gegenüber Beteiligten und Organen", "@category": "L", "@link": "AP", "@charttype": "A", "@gifi": "201", "@symbol_link": "", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12413", "@accno": "2020", "@description": "Verbindlichkeiten aus Lieferungen und Leistungen gegenüber Beteiligungen", "@category": "L", "@link": "AP", "@charttype": "A", "@gifi": "202", "@symbol_link": "", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10674", "@accno": "2021", "@description": "testnok", "@category": "L", "@link": "AR_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "202", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10062", "@accno": "2030", "@description": "Erhaltene Anzahlungen von <PERSON> (Belastung)", "@category": "L", "@link": "AR_paid", "@charttype": "A", "@gifi": "203", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10545", "@accno": "2031", "@description": "Erhaltene Anzahlungen von <PERSON> (Artikel)", "@category": "L", "@link": "IC_income:IC_sale", "@charttype": "A", "@gifi": "203", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "22091", "@accno": "2040", "@description": "TEST", "@category": "L", "@link": "AR_amount:AP_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "204", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10666", "@accno": "2050", "@description": "Gutscheine", "@category": "L", "@link": "AR_amount:AP_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "203", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "22125", "@accno": "2051", "@description": "TEST", "@category": "L", "@link": "AR_amount:AP_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "205", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10667", "@accno": "2055", "@description": "Gutscheine eingelöst", "@category": "L", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "203", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12669", "@accno": "2160", "@description": "Kontokorrent Kreditkarte <PERSON>", "@category": "L", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10259", "@accno": "216000", "@description": "Kontokorrent Gesellschafter", "@category": "L", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "216", "@symbol_link": "kontokorrent", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "22176", "@accno": "2161", "@description": "Kontokorrent <PERSON>", "@category": "L", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11167", "@accno": "2166", "@description": "Kreditkarte", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12666", "@accno": "21660", "@description": "zahlung und löschen", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12160", "@accno": "2169", "@description": "Kontokorrent Giuseppe", "@category": "L", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13064", "@accno": "218011", "@description": "Kreditkarte Test Antje", "@category": "L", "@link": "AP_paid", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10434", "@accno": "2185", "@description": "Kreditkarte alt", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "216", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10067", "@accno": "2200", "@description": "Geschuldete MWST", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10284", "@accno": "2201", "@description": "MWST 8,0%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.08", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.08", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21375", "@accno": "22010", "@description": "MWST 8.1%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.081", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.081", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10781", "@accno": "22012", "@description": "MWST 7.7%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.077", "@charttype": "A", "@gifi": "220", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10285", "@accno": "2202", "@description": "MWST 3,8%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.038", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.038", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10837", "@accno": "22020", "@description": "MWST 3,7%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.037", "@charttype": "A", "@gifi": "220", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.037", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21376", "@accno": "22021", "@description": "MWST 3.8%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.038", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.038", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10286", "@accno": "2203", "@description": "MWST 2,5%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.025", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.025", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21377", "@accno": "22031", "@description": "MWST 2.6%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.026", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.026", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10287", "@accno": "2204", "@description": "Bezugsteuer 8,0%", "@category": "L", "@link": "AP_tax", "@tax_rate": "0.08", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.08", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10794", "@accno": "22040", "@description": "Bezugsteuer 7,7%", "@category": "L", "@link": "AP_tax", "@tax_rate": "0.077", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21378", "@accno": "22041", "@description": "Bezugsteuer 8.1%", "@category": "L", "@link": "AP_tax", "@tax_rate": "0.081", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.081", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10288", "@accno": "22050", "@description": "Befreite Leistungen, Exporte MWST 0% (Ziff. 220)", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10289", "@accno": "22051", "@description": "Leistungen im Ausland MWST 0% (Ziff. 221)", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10290", "@accno": "22052", "@description": "Nicht steuerbare Leistungen MWST 0% (Ziff. 230)", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10291", "@accno": "22053", "@description": "Subventionen MWST 0% (Ziff. 900)", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10292", "@accno": "22054", "@description": "<PERSON><PERSON>den, Dividenden, Schadenersatz MWST 0% (Ziff. 910)", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10293", "@accno": "22055", "@description": "Nichtentgelt MWST 0%", "@category": "L", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10068", "@accno": "2206", "@description": "Verrechnungssteuer", "@category": "L", "@link": "AP_amount", "@tax_rate": "0", "@charttype": "A", "@gifi": "220", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10070", "@accno": "2215", "@description": "Kreditkarte", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "221", "@symbol_link": "k<PERSON><PERSON><PERSON><PERSON>", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10261", "@accno": "2261", "@description": "Beschlossene Ausschüttungen", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "226", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10073", "@accno": "2270", "@description": "Kontokorrent Vorsorgeeinrichtung", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "227", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10074", "@accno": "2271", "@description": "Kontokorrent AHV, IV, EO, ALV", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "227", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10075", "@accno": "2272", "@description": "Kontokorrent FAK", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "227", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10076", "@accno": "2273", "@description": "Kontokorrent Unfallversicherung", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "227", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10077", "@accno": "2274", "@description": "Kontokorrent Krankentaggeldversicherung", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "227", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10078", "@accno": "2279", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "L", "@link": "AP_amount", "@charttype": "A", "@gifi": "227", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10080", "@accno": "2300", "@description": "<PERSON>ch nicht bezahlter Aufwand", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "230", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10081", "@accno": "2301", "@description": "Erhaltener Ertrag des Folgejahrs", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "230", "@symbol_link": "passiverechnungsabgrenzung", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13648", "@accno": "2302", "@description": "Passive Abgrenzung", "@category": "L", "@link": "AP", "@charttype": "A", "@gifi": "230", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "11850", "@accno": "2310", "@description": "Passive Rechnungsabgrenzung Kreditoren", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "230", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10082", "@accno": "2330", "@description": "Kurzfristige Rückstellungen", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "230", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10085", "@accno": "2400", "@description": "Bankverbindlichkeiten langfristig", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "240", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10091", "@accno": "2600", "@description": "Rückstellungen", "@category": "L", "@link": "", "@charttype": "A", "@gifi": "260", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10266", "@accno": "2800", "@description": "Grundkapital", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "280", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10268", "@accno": "2900", "@description": "Aufgeld (Agio) bei Gründung oder Kapitalerhöhung", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "290", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10269", "@accno": "2950", "@description": "Allgemeine gesetzliche Gewinnreserve", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "290", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12414", "@accno": "2951", "@description": "Aufwertungsreserve", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "290", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12415", "@accno": "2952", "@description": "Reserve für eigene Aktien", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "290", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10270", "@accno": "2960", "@description": "Andere Reserven", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "290", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12416", "@accno": "2961", "@description": "Statutarische und beschlussmässige Gewinnreserven", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "290", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10272", "@accno": "2970", "@description": "Gewinnvortrag oder Verlustvortrag", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "297", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10273", "@accno": "2979", "@description": "Jahresgewinn oder Jahresverlust", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "297", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10275", "@accno": "2980", "@description": "Eigene Aktien", "@category": "Q", "@link": "", "@charttype": "A", "@gifi": "298", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10094", "@accno": "3000", "@description": "Produktionserlöse", "@category": "I", "@link": "AR_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "300", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10096", "@accno": "3200", "@description": "Handelserlöse", "@category": "I", "@link": "IC_income", "@charttype": "A", "@gifi": "320", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13804", "@accno": "3301", "@description": "Room Revenue", "@category": "I", "@link": "IC_income", "@charttype": "A", "@gifi": "33", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13823", "@accno": "3310", "@description": "Food Revenue", "@category": "I", "@link": "", "@charttype": "H", "@gifi": "3310", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "13808", "@accno": "33101", "@description": "Bar Revenue", "@category": "I", "@link": "AR_amount:AP_amount", "@charttype": "A", "@gifi": "3310", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13824", "@accno": "33102", "@description": "Self Service Restaurant Revenue", "@category": "I", "@link": "AR_amount:AP_amount", "@charttype": "A", "@gifi": "3310", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10098", "@accno": "3400", "@description": "Monatsabos", "@category": "I", "@link": "AR_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "340", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13283", "@accno": "3401", "@description": "Energie", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "340", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12467", "@accno": "3402", "@description": "Dienstunserlös 510", "@category": "I", "@link": "AR_amount", "@charttype": "A", "@gifi": "340", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "13554", "@accno": "3403", "@description": "Dienstunserlös 510", "@category": "I", "@link": "AR_amount:IC_sale", "@charttype": "A", "@gifi": "340", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12216", "@accno": "3407", "@description": "Dienstunserlös 98445", "@category": "I", "@link": "AR_amount", "@charttype": "A", "@gifi": "340", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12646", "@accno": "3408", "@description": "Dienstunserlö<PERSON>", "@category": "I", "@link": "AR_amount", "@charttype": "A", "@gifi": "340", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "11003", "@accno": "3410", "@description": "Dienstleistungserlöse 10", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "341", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10680", "@accno": "3497", "@description": "MWST 8,0% Saldo", "@category": "I", "@link": "AR_tax:IC_taxpart:IC_taxservice", "@tax_rate": "0.08", "@charttype": "A", "@gifi": "340", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.08", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "13784", "@accno": "3500", "@description": "Bar Revenue", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "350", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10100", "@accno": "3600", "@description": "Nebenerlöse aus Lieferungen und Leistungen", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "360", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10101", "@accno": "3680", "@description": "Sonstige Erlöse", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "360", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10102", "@accno": "3689", "@description": "<PERSON><PERSON> kont<PERSON>t", "@category": "I", "@link": "AR_amount", "@charttype": "A", "@gifi": "360", "@symbol_link": "nichtkontierterertrag", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10103", "@accno": "3699", "@description": "Systemkonto", "@category": "I", "@link": "AR_amount:IC_sale:IC_income", "@charttype": "A", "@gifi": "360", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10105", "@accno": "3700", "@description": "Eigenleistungen", "@category": "I", "@link": "", "@charttype": "A", "@gifi": "370", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10106", "@accno": "3710", "@description": "Eigenverbrauch", "@category": "I", "@link": "", "@charttype": "A", "@gifi": "370", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10108", "@accno": "3800", "@description": "<PERSON><PERSON><PERSON>", "@category": "I", "@link": "AR_paid", "@charttype": "A", "@gifi": "380", "@symbol_link": "debitorskonto", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10109", "@accno": "3801", "@description": "Rabatte und Preisnachlässe", "@category": "I", "@link": "AR_paid", "@charttype": "A", "@gifi": "380", "@symbol_link": "debitordifferenz", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "21649", "@accno": "38011", "@description": "Rabatte", "@category": "I", "@link": "AR_amount", "@charttype": "A", "@gifi": "380", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "21650", "@accno": "38012", "@description": "Rundungsdifferenzen", "@category": "I", "@link": "AR_amount", "@charttype": "A", "@gifi": "380", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10110", "@accno": "3804", "@description": "Inkassospesen", "@category": "I", "@link": "", "@charttype": "A", "@gifi": "380", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10111", "@accno": "3805", "@description": "Verluste Forderungen, Veränderung Wertberichtigungen", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "380", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "14001", "@accno": "3807", "@description": "Gebühren Sumup", "@category": "I", "@link": "AR_amount:IC_income", "@charttype": "A", "@gifi": "380", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10113", "@accno": "3900", "@description": "Bestandesänderungen unfertige Erzeugnisse", "@category": "I", "@link": "", "@charttype": "A", "@gifi": "390", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10114", "@accno": "3901", "@description": "Bestandesänderungen fertige Erzeugnisse", "@category": "I", "@link": "", "@charttype": "A", "@gifi": "390", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10115", "@accno": "3904", "@description": "Bestandesänderungen nicht fakturierte Dienstleistungen", "@category": "I", "@link": "", "@charttype": "A", "@gifi": "390", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10762", "@accno": "4000", "@description": "<PERSON><PERSON><PERSON> nichts", "@category": "E", "@link": "IC_taxservice", "@tax_rate": "0", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "10964", "@accno": "4000.dot.test", "@description": "<PERSON><PERSON><PERSON> nichts", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10963", "@accno": "4000-minus-test", "@description": "<PERSON><PERSON><PERSON> nichts", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10962", "@accno": "4000_underline_test", "@description": "<PERSON><PERSON><PERSON> nichts", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10874", "@accno": "4001", "@description": "<PERSON><PERSON><PERSON>n", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10764", "@accno": "4010", "@description": "<PERSON><PERSON><PERSON> garnichts", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10120", "@accno": "4200", "@description": "Einkauf Handelsware", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "420", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10763", "@accno": "4220", "@description": "Einkauf Handelsware 2", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "420", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11910", "@accno": "4300", "@description": "Test Rot", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11911", "@accno": "43000", "@description": "Test Rot", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "400", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10122", "@accno": "4400", "@description": "Einkauf Drittleistung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "440", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10127", "@accno": "4800", "@description": "Bestandesänderungen Handelswaren", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "480", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10128", "@accno": "4880", "@description": "Materialverluste", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "480", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10130", "@accno": "4999", "@description": "Systemkonto", "@category": "E", "@link": "AP_amount:IC_expense:IC_cogs", "@charttype": "A", "@gifi": "490", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10132", "@accno": "5400", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "540", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10133", "@accno": "5401", "@description": "Zulagen", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "540", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10134", "@accno": "5402", "@description": "Erfolgsbeteiligungen", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "540", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10135", "@accno": "5403", "@description": "Provisionen", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "540", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10136", "@accno": "5405", "@description": "Leistungen von Sozialversicherungen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "540", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10138", "@accno": "5700", "@description": "AHV, IV, EO, ALV", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "570", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10139", "@accno": "5710", "@description": "FAK", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "570", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10140", "@accno": "5720", "@description": "Vorsorgeeinrichtungen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "570", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10141", "@accno": "5730", "@description": "Unfallversicherung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "570", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10142", "@accno": "5740", "@description": "Krankentaggeldversicherung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "570", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10143", "@accno": "5790", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "570", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10145", "@accno": "5800", "@description": "Personalinserate", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10146", "@accno": "5810", "@description": "Aus- und Weiterbildung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10147", "@accno": "5820", "@description": "Spesenentschädigung effektiv", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10148", "@accno": "5830", "@description": "Spesenentschädigung pauschal", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10149", "@accno": "5840", "@description": "<PERSON><PERSON><PERSON>", "@category": "E", "@link": "AP_amount:IC_expense", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10150", "@accno": "5880", "@description": "<PERSON><PERSON><PERSON>", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "21346", "@accno": "5888", "@description": "Super Personalaufwand", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "580", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10151", "@accno": "5890", "@description": "Privatanteile <PERSON>", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "580", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10153", "@accno": "5900", "@description": "Leistungen Dritter", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "590", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10156", "@accno": "6000", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "600", "@symbol_link": "miete", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10833", "@accno": "6010", "@description": "Mieten Parkplatz", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "600", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10157", "@accno": "6030", "@description": "<PERSON><PERSON><PERSON><PERSON>", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "600", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10158", "@accno": "6040", "@description": "Reinigung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "600", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10159", "@accno": "6090", "@description": "Privat<PERSON><PERSON>", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "600", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10161", "@accno": "6130", "@description": "URE Büromobiliar", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "610", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10162", "@accno": "6160", "@description": "Leasing mobile Sachanlagen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "610", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10164", "@accno": "6200", "@description": "Reparaturen, Service, Reinigung Fahrzeuge", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "13375", "@accno": "6202", "@description": "<PERSON><PERSON> du<PERSON>u", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12217", "@accno": "6203", "@description": "TEST TEST", "@category": "E", "@link": "AP_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "620", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "12647", "@accno": "6204", "@description": "Baum", "@category": "E", "@link": "AP_amount:IC_income:IC_sale", "@charttype": "A", "@gifi": "620", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "13555", "@accno": "6205", "@description": "<PERSON><PERSON> du<PERSON>u", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@allow_gl": "false", "@is_active": "true", "taxes": null}, {"@id": "10165", "@accno": "6210", "@description": "Betriebsstoffe Fahrzeuge", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10166", "@accno": "6220", "@description": "Versicherungen Fahrzeuge", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10167", "@accno": "6230", "@description": "Verkehrsabgaben, Beiträge, Gebühren", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10168", "@accno": "6260", "@description": "Fahrzeugleasing, Fahrzeugmieten", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10169", "@accno": "6270", "@description": "Privatanteil Fahrzeugaufwand", "@category": "E", "@link": "AR_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10170", "@accno": "6280", "@description": "Transportaufwand", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "620", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10172", "@accno": "6300", "@description": "Sachversicherungen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "630", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10173", "@accno": "6360", "@description": "Abgaben und Gebühren", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "630", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10174", "@accno": "6370", "@description": "Bewilligungen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "630", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10176", "@accno": "6400", "@description": "Elektrizität", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "640", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10177", "@accno": "6460", "@description": "Entsorgungsaufwand", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "640", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10180", "@accno": "6500", "@description": "Büromaterial", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12667", "@accno": "65000", "@description": "buchung und löschen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10181", "@accno": "6501", "@description": "Drucksachen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10182", "@accno": "6503", "@description": "Fachliteratur", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10183", "@accno": "6510", "@description": "Telekommunikation", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10184", "@accno": "6513", "@description": "Porti", "@category": "E", "@link": "AP_amount:IC_expense", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10185", "@accno": "6520", "@description": "Be<PERSON>räge, Spenden, Vergabungen, Trinkgelder", "@category": "E", "@link": "AP_amount:IC_expense", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10186", "@accno": "6530", "@description": "Buchführung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10187", "@accno": "6531", "@description": "Unternehmensberatung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10188", "@accno": "6532", "@description": "Rechtsberatung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10189", "@accno": "6540", "@description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Generalversammlung", "@category": "E", "@link": "AP_amount:IC_expense", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10190", "@accno": "6542", "@description": "Revisionsstelle", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10191", "@accno": "6550", "@description": "Gründungs-, Kapitalerhöhungs- und Organisationsaufwand", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10192", "@accno": "6551", "@description": "Inkasso- und Betreibungsaufwand", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "650", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10194", "@accno": "6570", "@description": "Leasing Hardware und Software", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "657", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10195", "@accno": "6580", "@description": "<PERSON><PERSON><PERSON>, Updates", "@category": "E", "@link": "AP_amount:IC_expense", "@charttype": "A", "@gifi": "657", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10196", "@accno": "6581", "@description": "Hosting und Wartung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "657", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10197", "@accno": "6583", "@description": "Verbrauchsmaterial IT", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "657", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10198", "@accno": "6590", "@description": "Beratung und Entwicklung IT", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "657", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10200", "@accno": "6600", "@description": "Werbeinserate", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10201", "@accno": "6610", "@description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10202", "@accno": "6620", "@description": "Fachmessen, Ausstellungen, Dekoration", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10203", "@accno": "6640", "@description": "Reisespesen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10204", "@accno": "6641", "@description": "Kundenbetreuung", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10205", "@accno": "6660", "@description": "Sponsoring", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10206", "@accno": "6670", "@description": "<PERSON><PERSON><PERSON><PERSON><PERSON>itsarbeit, Public Relations", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10207", "@accno": "6680", "@description": "<PERSON><PERSON><PERSON><PERSON>tung, Marktanalysen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "660", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10209", "@accno": "6700", "@description": "Sonstiger betrieblicher Aufwand", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "670", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10836", "@accno": "6730", "@description": "Vorsteuerkorrektur", "@category": "E", "@link": "AP_tax", "@tax_rate": "0", "@charttype": "A", "@gifi": "670", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": {"rate": "0.0", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00"}}}, {"@id": "21312", "@accno": "67300_inaktivab010121", "@description": "Vorsteuerkorrektur alt", "@category": "E", "@link": "AP_tax", "@tax_rate": "0", "@charttype": "A", "@gifi": "670", "@allow_gl": "true", "@is_active": "true", "taxes": {"tax": [{"rate": "0.077", "taxnumber": "CHE-111.111.111 MWST", "valid_from": "1970-01-01T01:00:00+01:00", "valid_to": "2023-01-01T00:00:00+01:00"}, {"rate": "0.088", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00", "valid_to": "2024-01-01T00:00:00+01:00"}, {"rate": "0.0", "taxnumber": "", "valid_from": "1970-01-01T01:00:00+01:00"}]}}, {"@id": "10210", "@accno": "6799", "@description": "<PERSON><PERSON> kont<PERSON>t", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "670", "@symbol_link": "nichtkontierteraufwand", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10212", "@accno": "6800", "@description": "Wertberichtigungen", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "680", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10213", "@accno": "6820", "@description": "Abschreibungen", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "680", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10215", "@accno": "6900", "@description": "Zinsaufwand gegenüber Dritten", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "690", "@symbol_link": "schu<PERSON><PERSON><PERSON>", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10276", "@accno": "6920", "@description": "Zinsaufwand gegenüber Beteiligten und Organen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "690", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10216", "@accno": "6930", "@description": "Zinsaufwand gegenüber Vorsorgeeinrichtungen", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "690", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10217", "@accno": "6940", "@description": "Bankspesen", "@category": "E", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "690", "@symbol_link": "d<PERSON>tspesen", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10218", "@accno": "6942", "@description": "Kursverluste", "@category": "E", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "690", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10219", "@accno": "6943", "@description": "Kreditkartengebühr", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "690", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10220", "@accno": "6944", "@description": "Bankspesen Zahlungsverkehr, Rundungsdifferenzen", "@category": "E", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "690", "@symbol_link": "kreditordifferenz", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10222", "@accno": "6950", "@description": "Erträge aus flüssigen Mitteln und Wertschriften", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "695", "@symbol_link": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10223", "@accno": "6952", "@description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "695", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11100", "@accno": "6990", "@description": "Aufwände aus Wertschriften des Anlagevermögens (nicht realisierte Erfolge)", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "695", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "11101", "@accno": "6991", "@description": "Erträge aus Wertschriften des Anlagevermögens (nicht realisierte Erfolge)", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "695", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10226", "@accno": "7000", "@description": "Ertrag aus Nebenbetrieb", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "700", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10228", "@accno": "7010", "@description": "Aufwand aus Nebenbetrieb", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "701", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10231", "@accno": "8000", "@description": "Betriebsfremder Aufwand", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "800", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10233", "@accno": "8100", "@description": "Betriebsfremder Ertrag", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "810", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10235", "@accno": "8500", "@description": "Ausserordentlicher Aufwand", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "850", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10237", "@accno": "8510", "@description": "Ausserordentlicher Ertrag", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "851", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10239", "@accno": "8600", "@description": "<PERSON><PERSON><PERSON>", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "860", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10241", "@accno": "8610", "@description": "Einmaliger Ertrag", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "861", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10243", "@accno": "8700", "@description": "Periodenfremder Aufwand", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "870", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10245", "@accno": "8710", "@description": "Periodenfremder Ertrag", "@category": "E", "@link": "", "@charttype": "A", "@gifi": "871", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10118", "@accno": "8888", "@description": "Testkonto8", "@category": "E", "@link": "AR_paid:AP_paid:IC_income:IC_sale", "@charttype": "A", "@gifi": "880", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10505", "@accno": "8889", "@description": "Testkonto9", "@category": "E", "@link": "AR_paid:AP_paid:IC_income:IC_sale", "@charttype": "A", "@gifi": "880", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10247", "@accno": "8900", "@description": "Kantons- und Gemeindesteuern", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "890", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "10248", "@accno": "8901", "@description": "Direkte Bundessteuern", "@category": "E", "@link": "AP_amount", "@charttype": "A", "@gifi": "890", "@symbol_link": "", "@allow_gl": "true", "@is_active": "true", "taxes": null}, {"@id": "12581", "@accno": "CH63 007001147510 121211", "@description": "UBS", "@category": "A", "@link": "AR_paid:AP_paid", "@charttype": "A", "@gifi": "100", "@allow_gl": "true", "@is_active": "true", "taxes": null}]}