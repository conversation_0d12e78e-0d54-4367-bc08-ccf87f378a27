import requests
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import json


class InfoniqaAPIClient:
    """Client für die Infoniqa Business Plus API"""

    def __init__(self, client_name: str, pat: str):
        self.client_name = client_name
        self.pat = pat
        self.base_url = "https://service.runmyaccounts.com/api"
        self.version = "latest"
        self.session = requests.Session()

        # Standard Headers inkl. Authentifizierung via PAT (Bearer Token)
        self.session.headers.update({
            'Accept': 'application/json',
            'User-Agent': 'ER-App/1.0',
            'Authorization': f'Bearer {self.pat}'
        })

    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Any:
        """
        Führt eine API-Anfrage aus

        Args:
            endpoint: API-Endpunkt
            params: Zusätzliche Parameter

        Returns:
            JSON-Response als Dictionary

        Raises:
            Exception: Bei API-Fehlern
        """
        if params is None:
            params = {}

        # Authentifizierung via Header Authorization: Bearer <PAT>

        url = f"{self.base_url}/{self.version}/clients/{self.client_name}/{endpoint}"

        try:
            logging.info(f"API-Anfrage: {url}")
            response = self.session.get(url, params=params, timeout=30)

            # Status Code prüfen
            if response.status_code == 401:
                raise Exception("Ungültiges PAT oder keine Berechtigung")
            elif response.status_code == 404:
                raise Exception(
                    "Mandant nicht gefunden oder Endpunkt existiert nicht")
            elif response.status_code != 200:
                raise Exception(
                    f"API-Fehler: HTTP {response.status_code} - {response.text}")

            # JSON-Response parsen
            try:
                data = response.json()
                logging.info(f"API-Antwort erhalten: {len(str(data))} Zeichen")
                return data
            except json.JSONDecodeError:
                raise Exception("Ungültige JSON-Antwort von der API")

        except requests.exceptions.Timeout:
            raise Exception("API-Anfrage hat das Zeitlimit überschritten")
        except requests.exceptions.ConnectionError:
            raise Exception("Verbindung zur API fehlgeschlagen")
        except requests.exceptions.RequestException as e:
            raise Exception(f"Netzwerkfehler: {str(e)}")

    def get_chart_of_accounts(self) -> List[Dict[str, Any]]:
        """
        Ruft den Kontenplan ab

        Returns:
            Liste der Konten
        """
        logging.info("Lade Kontenplan...")
        data = self._make_request("charts")

        # Debug: Datenstruktur analysieren
        import json
        with open('debug_charts_raw.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logging.info(f"Debug: Charts API Antwort-Typ: {type(data)}")
        if isinstance(data, dict):
            logging.info(f"Debug: Dict Keys: {list(data.keys())}")
        elif isinstance(data, list):
            logging.info(f"Debug: Liste mit {len(data)} Elementen")
            if len(data) > 0:
                logging.info(f"Debug: Erstes Element Typ: {type(data[0])}")
                if isinstance(data[0], dict):
                    logging.info(
                        f"Debug: Erstes Element Keys: {list(data[0].keys())}")

        # Datenstruktur prüfen und normalisieren
        accounts = []
        if isinstance(data, dict) and 'chart' in data:
            # API gibt {"chart": [...]} zurück
            chart_data = data['chart']
            if isinstance(chart_data, list):
                accounts = [self._normalize_account(
                    account) for account in chart_data]
            else:
                accounts = [self._normalize_account(chart_data)]
        elif isinstance(data, list):
            # Direkte Liste von Konten
            accounts = [self._normalize_account(account) for account in data]
        elif isinstance(data, dict):
            # Einzelnes Konto
            accounts = [self._normalize_account(data)]
        else:
            raise Exception("Unerwartete Datenstruktur im Kontenplan")

        # Debug: Normalisierte Konten speichern
        with open('debug_charts_normalized.json', 'w', encoding='utf-8') as f:
            json.dump(accounts, f, indent=2, ensure_ascii=False)

        logging.info(f"Kontenplan geladen: {len(accounts)} Konten")
        return accounts

    def _normalize_account(self, account_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalisiert ein Konto-Objekt

        Args:
            account_data: Rohe Konto-Daten von der API

        Returns:
            Normalisiertes Konto-Dictionary
        """
        return {
            'id': account_data.get('@id') or account_data.get('id'),
            'accno': account_data.get('@accno') or account_data.get('accno'),
            'description': account_data.get('@description') or account_data.get('description'),
            'link': account_data.get('@link') or account_data.get('link', ''),
            'allow_gl': account_data.get('@allow_gl') or account_data.get('allow_gl', 'true'),
            'is_active': account_data.get('@is_active') or account_data.get('is_active', 'true')
        }

    def get_departments(self) -> List[Dict[str, Any]]:
        """
        Ruft die Kostenstellen ab

        Returns:
            Liste der Kostenstellen
        """
        logging.info("Lade Kostenstellen...")
        data = self._make_request("departments")

        # Datenstruktur prüfen und normalisieren
        departments = []
        if isinstance(data, list):
            # Liste von Kostenstellen (Standard-Fall)
            departments = [self._normalize_department(dept) for dept in data]
        elif isinstance(data, dict):
            # Einzelne Kostenstelle (seltener Fall)
            departments = [self._normalize_department(data)]
        else:
            raise Exception("Unerwartete Datenstruktur bei Kostenstellen")

        logging.info(
            f"Kostenstellen geladen: {len(departments)} Kostenstellen")
        return departments

    def _normalize_department(self, dept_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalisiert eine Kostenstelle

        Args:
            dept_data: Rohe Kostenstellen-Daten von der API

        Returns:
            Normalisierte Kostenstelle
        """
        return {
            'id': dept_data.get('@id') or dept_data.get('id'),
            'name': dept_data.get('@name') or dept_data.get('name'),
            'role': dept_data.get('@role') or dept_data.get('role', '')
        }

    def get_transactions(self, account_number: str, date_from: datetime, date_to: datetime) -> List[Dict[str, Any]]:
        """
        Ruft Buchungen für ein Konto ab

        Args:
            account_number: Kontonummer
            date_from: Startdatum
            date_to: Enddatum

        Returns:
            Liste der Buchungen
        """
        logging.info(f"Lade Buchungen für Konto {account_number}...")

        params = {
            'from_date': date_from.strftime('%Y-%m-%d'),
            'to_date': date_to.strftime('%Y-%m-%d')
        }

        data = self._make_request(
            f"charts/{account_number}/transactions", params)

        # Datenstruktur prüfen und normalisieren
        transactions = []
        if isinstance(data, dict):
            if 'transactions' in data:
                # API gibt {"transactions": {"transaction": [...]}} zurück
                trans_container = data['transactions']
                if isinstance(trans_container, dict) and 'transaction' in trans_container:
                    trans_data = trans_container['transaction']
                    if isinstance(trans_data, list):
                        transactions = [self._normalize_transaction(
                            t) for t in trans_data]
                    else:
                        transactions = [
                            self._normalize_transaction(trans_data)]
                elif isinstance(trans_container, list):
                    transactions = [self._normalize_transaction(
                        t) for t in trans_container]
                else:
                    transactions = [
                        self._normalize_transaction(trans_container)]
            elif 'transaction' in data:
                # Direkte Transaktion
                trans_data = data['transaction']
                if isinstance(trans_data, list):
                    transactions = [self._normalize_transaction(
                        t) for t in trans_data]
                else:
                    transactions = [self._normalize_transaction(trans_data)]
            else:
                # Direkte Transaktion
                transactions = [self._normalize_transaction(data)]
        elif isinstance(data, list):
            transactions = [self._normalize_transaction(t) for t in data]
        else:
            raise Exception(
                f"Unerwartete Datenstruktur bei Buchungen für Konto {account_number}")

        logging.info(
            f"Buchungen für Konto {account_number} geladen: {len(transactions)} Buchungen")
        return transactions

    def _normalize_transaction(self, trans_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Normalisiert eine Transaktion

        Args:
            trans_data: Rohe Transaktions-Daten von der API

        Returns:
            Normalisierte Transaktion
        """
        # Department-Daten extrahieren
        department = trans_data.get('department', {})

        # Debug: Department-Struktur analysieren (nur im Log)
        logging.debug(
            f"Transaction {trans_data.get('id')}: Department = {department}")

        # Verschiedene mögliche Strukturen für department_id und name behandeln
        dept_id = None
        dept_name = None

        # Variante 1: {'@id': '123', '@name': 'Name'} (XML-Attribute mit @)
        if isinstance(department, dict) and '@id' in department:
            dept_id = department.get('@id')
            dept_name = department.get('@name')
        # Variante 2: {'_id': '123', '_name': 'Name'} (Underscore-Prefix)
        elif isinstance(department, dict) and '_id' in department:
            dept_id = department.get('_id')
            dept_name = department.get('_name')
        # Variante 3: {'id': '123', 'name': 'Name'} (Standard)
        elif isinstance(department, dict) and 'id' in department:
            dept_id = department.get('id')
            dept_name = department.get('name')
        # Variante 4: String-ID
        elif isinstance(department, str) and department:
            dept_id = department
            dept_name = f'Kostenstelle {department}'

        # Fallback
        if not dept_id:
            dept_id = '0'
        if not dept_name:
            dept_name = f'Kostenstelle {dept_id}'

        # Extract role information if department is a dictionary
        dept_role = ''
        if isinstance(department, dict):
            dept_role = department.get('_role') or department.get('role', '')

        # Chart-Daten extrahieren
        chart = trans_data.get('chart', {})

        return {
            'id': trans_data.get('id'),
            'transdate': trans_data.get('transdate'),
            'reference': trans_data.get('reference', ''),
            'description': trans_data.get('description', ''),
            'amount': trans_data.get('amount', '0'),
            'module': trans_data.get('module', ''),
            'source': trans_data.get('source', ''),
            'entry_id': trans_data.get('entry_id'),
            'memo': trans_data.get('memo', ''),
            'notes': trans_data.get('notes', ''),
            'accno': trans_data.get('accno'),
            'department': {
                'id': dept_id,
                'name': dept_name,
                'role': dept_role
            },
            'chart': {
                'id': chart.get('_id') or chart.get('id'),
                'accno': chart.get('_accno') or chart.get('accno'),
                'link': chart.get('_link') or chart.get('link', ''),
                'allow_gl': chart.get('_allow_gl') or chart.get('allow_gl', 'false'),
                'is_active': chart.get('_is_active') or chart.get('is_active', 'true')
            }
        }

    def get_all_transactions_with_departments(self, date_from: datetime, date_to: datetime) -> Dict[str, List[Dict[str, Any]]]:
        """
        Optimierte Methode: Ruft alle Buchungen in einem API-Call ab und ergänzt Kostenstellen-Informationen

        Diese Methode kombiniert die Effizienz der /charts/x/transactions API mit der
        Vollständigkeit der Kostenstellen-Informationen aus einzelnen Abfragen.

        Args:
            date_from: Startdatum
            date_to: Enddatum

        Returns:
            Dictionary mit Kontonummer als Key und Buchungen als Value (MIT Kostenstellen)
        """
        from collections import defaultdict
        import json

        logging.info(
            "Optimierte Methode: Lade alle Buchungen mit Kostenstellen...")

        # Schritt 1: Alle Buchungen mit einem API-Call abrufen (ohne Kostenstellen)
        params = {
            'from_date': date_from.strftime('%Y-%m-%d'),
            'to_date': date_to.strftime('%Y-%m-%d')
        }

        # Alle Buchungen mit "x" als Platzhalter für alle Konten abrufen
        data = self._make_request("charts/x/transactions", params)

        # Debug: Datenstruktur analysieren
        with open('debug_all_transactions_raw.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        # Buchungen nach Konten gruppieren
        all_transactions = defaultdict(list)
        transaction_ids = set()  # Für Schritt 2: Eindeutige Transaktions-IDs sammeln

        # Datenstruktur verarbeiten
        transactions = []
        if isinstance(data, dict) and 'transaction' in data:
            # Direkte transaction-Liste (für /charts/x/transactions)
            trans_data = data['transaction']
            if isinstance(trans_data, list):
                transactions = [self._normalize_transaction(
                    t) for t in trans_data]
            else:
                transactions = [self._normalize_transaction(trans_data)]

        # Buchungen nach Kontonummer gruppieren und Transaktions-IDs sammeln
        for transaction in transactions:
            account_number = transaction.get('accno')
            transaction_id = transaction.get('id')
            if account_number:
                all_transactions[account_number].append(transaction)
                if transaction_id:
                    transaction_ids.add(transaction_id)

        logging.info(
            f"Schritt 1: {len(transactions)} Buchungen für {len(all_transactions)} Konten geladen")
        logging.info(
            f"Schritt 1: {len(transaction_ids)} eindeutige Transaktions-IDs gefunden")

        # Schritt 2: Kostenstellen-Informationen aus einzelnen Kontoabfragen ergänzen
        # Wir wählen ein Konto mit vielen Buchungen für die Kostenstellen-Informationen
        if all_transactions:
            # Konto mit den meisten Buchungen finden
            sample_account = max(all_transactions.items(),
                                 key=lambda x: len(x[1]))[0]
            logging.info(
                f"Schritt 2: Verwende Konto {sample_account} für Kostenstellen-Informationen")

            # Buchungen für dieses Konto abrufen (mit Kostenstellen)
            sample_transactions = self.get_transactions(
                sample_account, date_from, date_to)

            # Kostenstellen-Mapping erstellen: transaction_id -> department_info
            dept_mapping = {}
            for transaction in sample_transactions:
                trans_id = transaction.get('id')
                if trans_id and 'department' in transaction:
                    dept_mapping[trans_id] = transaction['department']

            logging.info(
                f"Schritt 2: {len(dept_mapping)} Kostenstellen-Informationen gefunden")

            # Debug: Kostenstellen-Mapping speichern
            with open('debug_department_mapping.json', 'w', encoding='utf-8') as f:
                json.dump(dept_mapping, f, indent=2, ensure_ascii=False)

            # Schritt 3: Kostenstellen-Informationen zu allen Buchungen hinzufügen
            updated_count = 0
            for account_number, account_transactions in all_transactions.items():
                for transaction in account_transactions:
                    trans_id = transaction.get('id')
                    if trans_id in dept_mapping:
                        transaction['department'] = dept_mapping[trans_id]
                        updated_count += 1

            logging.info(
                f"Schritt 3: {updated_count} Buchungen mit Kostenstellen-Informationen aktualisiert")

        # Debug: Gruppierte Buchungen mit Kostenstellen speichern
        with open('debug_all_transactions_with_departments.json', 'w', encoding='utf-8') as f:
            json.dump(dict(all_transactions), f, indent=2,
                      ensure_ascii=False, default=str)

        logging.info(
            f"Optimierte Methode abgeschlossen: {len(transactions)} Buchungen für {len(all_transactions)} Konten")
        return dict(all_transactions)

    def get_all_transactions_optimized(self, date_from: datetime, date_to: datetime) -> Dict[str, List[Dict[str, Any]]]:
        """
        Ruft alle Buchungen für alle Konten in einem API-Call ab (optimiert)

        WICHTIG: Diese Methode liefert KEINE Kostenstellen-Informationen!
        Die /charts/x/transactions API enthält keine department-Daten.

        Args:
            date_from: Startdatum
            date_to: Enddatum

        Returns:
            Dictionary mit Kontonummer als Key und Buchungen als Value (OHNE Kostenstellen!)
        """
        from collections import defaultdict

        logging.info(
            "Lade alle Buchungen optimiert (OHNE Kostenstellen-Daten)...")

        params = {
            'from_date': date_from.strftime('%Y-%m-%d'),
            'to_date': date_to.strftime('%Y-%m-%d')
        }

        # Alle Buchungen mit "x" als Platzhalter für alle Konten abrufen
        data = self._make_request("charts/x/transactions", params)

        # Debug: Datenstruktur analysieren
        import json
        with open('debug_all_transactions_raw.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        # Buchungen nach Konten gruppieren
        all_transactions = defaultdict(list)

        # Datenstruktur verarbeiten
        transactions = []
        if isinstance(data, dict):
            if 'transaction' in data:
                # Direkte transaction-Liste (für /charts/x/transactions)
                trans_data = data['transaction']
                if isinstance(trans_data, list):
                    transactions = [self._normalize_transaction(
                        t) for t in trans_data]
                else:
                    transactions = [self._normalize_transaction(trans_data)]
            elif 'transactions' in data:
                # Verschachtelte Struktur (für einzelne Konten)
                trans_container = data['transactions']
                if isinstance(trans_container, dict) and 'transaction' in trans_container:
                    trans_data = trans_container['transaction']
                    if isinstance(trans_data, list):
                        transactions = [self._normalize_transaction(
                            t) for t in trans_data]
                    else:
                        transactions = [
                            self._normalize_transaction(trans_data)]
                elif isinstance(trans_container, list):
                    transactions = [self._normalize_transaction(
                        t) for t in trans_container]
        elif isinstance(data, list):
            transactions = [self._normalize_transaction(t) for t in data]

        # Buchungen nach Kontonummer gruppieren
        for transaction in transactions:
            account_number = transaction.get('accno')
            if account_number:
                all_transactions[account_number].append(transaction)

        # Debug: Gruppierte Buchungen speichern
        with open('debug_all_transactions_grouped.json', 'w', encoding='utf-8') as f:
            json.dump(dict(all_transactions), f, indent=2,
                      ensure_ascii=False, default=str)

        logging.info(
            f"Alle Buchungen geladen: {len(transactions)} Buchungen für {len(all_transactions)} Konten")
        logging.warning(
            "ACHTUNG: Diese Buchungen enthalten KEINE Kostenstellen-Informationen!")
        return dict(all_transactions)

    def get_all_transactions(self, accounts: List[Dict[str, Any]], date_from: datetime, date_to: datetime) -> Dict[str, List[Dict[str, Any]]]:
        """
        Ruft alle Buchungen für eine Liste von Konten ab

        Args:
            accounts: Liste der Konten
            date_from: Startdatum
            date_to: Enddatum

        Returns:
            Dictionary mit Kontonummer als Key und Buchungen als Value
        """
        # Versuche zuerst die optimierte Methode mit Kostenstellen-Ergänzung
        try:
            return self.get_all_transactions_with_departments(date_from, date_to)
        except Exception as e:
            logging.warning(
                f"Optimierte Buchungsabfrage mit Kostenstellen fehlgeschlagen: {str(e)}")
            logging.info("Fallback auf einzelne Kontoabfragen...")

            # Fallback auf einzelne Abfragen für vollständige Daten
        all_transactions = {}

        for i, account in enumerate(accounts):
            account_number = account.get('accno') or account.get(
                'account_number') or account.get('number')

            if not account_number:
                logging.warning(f"Konto ohne Nummer gefunden: {account}")
                continue

            try:
                transactions = self.get_transactions(
                    account_number, date_from, date_to)
                all_transactions[account_number] = transactions

                # Progress-Callback könnte hier implementiert werden
                progress = (i + 1) / len(accounts)
                logging.info(f"Fortschritt Buchungen: {progress:.1%}")

            except Exception as e:
                logging.error(
                    f"Fehler beim Laden der Buchungen für Konto {account_number}: {str(e)}")
                # Leere Liste für fehlgeschlagene Konten
                all_transactions[account_number] = []

        return all_transactions

    def test_connection(self) -> bool:
        """
        Testet die Verbindung zur API

        Returns:
            True wenn Verbindung erfolgreich
        """
        try:
            # Einfacher Test mit Charts-Endpunkt
            self._make_request("charts")
            return True
        except Exception as e:
            logging.error(f"Verbindungstest fehlgeschlagen: {str(e)}")
            return False
