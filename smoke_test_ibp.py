import os
import json
import sys
from api_client import InfoniqaAPIClient

def main():
    mandant = os.environ.get('MANDANT') or 'musterfirma'
    pat = os.environ.get('PAT')
    if not pat:
        print('Missing PAT environment variable', file=sys.stderr)
        sys.exit(1)

    print('Creating client...')
    client = InfoniqaAPIClient(mandant, pat)

    print('Testing connection...')
    ok = client.test_connection()
    print('test_connection:', ok)

    if not ok:
        print('Connection test failed.')
        sys.exit(2)

    print('Fetching chart of accounts (preview)...')
    accounts = client.get_chart_of_accounts()
    print('accounts_count:', len(accounts))
    preview = [{ 'accno': a.get('accno'), 'description': a.get('description') } for a in accounts[:3]]
    print('preview_json:', json.dumps(preview, ensure_ascii=False))

if __name__ == '__main__':
    main()

